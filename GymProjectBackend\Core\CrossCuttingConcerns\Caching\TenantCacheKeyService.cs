using Core.Utilities.Security.CompanyContext;
using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Tenant bazlı cache key oluşturma servisi
    /// Her tenant için ayrı cache namespace'i sağlar
    /// </summary>
    public class TenantCacheKeyService : ICacheKeyService
    {
        private readonly ICompanyContext _companyContext;

        public TenantCacheKeyService(ICompanyContext companyContext)
        {
            _companyContext = companyContext;
        }

        public string GenerateKey(string entityName, string operation, params object[] parameters)
        {
            var tenantId = _companyContext.GetCompanyId();
            var paramHash = GenerateParameterHash(parameters);
            
            // Format: T{TenantId}:E_{EntityName}:O_{Operation}:P_{ParamHash}
            return $"T{tenantId}:E_{entityName}:O_{operation}:P_{paramHash}";
        }

        public string GenerateListKey(string entityName, string operation = "GetAll")
        {
            var tenantId = _companyContext.GetCompanyId();
            return $"T{tenantId}:E_{entityName}:O_{operation}";
        }

        public string GenerateDetailKey(string entityName, object id)
        {
            var tenantId = _companyContext.GetCompanyId();
            return $"T{tenantId}:E_{entityName}:O_GetById:P_{id}";
        }

        public string GeneratePatternKey(string entityName)
        {
            var tenantId = _companyContext.GetCompanyId();
            return $"T{tenantId}:E_{entityName}:*";
        }

        public string GenerateTenantPatternKey(int tenantId)
        {
            return $"T{tenantId}:*";
        }

        /// <summary>
        /// Parametrelerden hash oluşturur
        /// </summary>
        private string GenerateParameterHash(object[] parameters)
        {
            if (parameters == null || parameters.Length == 0)
                return "NONE";

            try
            {
                var combined = string.Join("|", parameters.Select(p => p?.ToString() ?? "NULL"));
                using var sha256 = SHA256.Create();
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                return Convert.ToBase64String(hash)[..8]; // İlk 8 karakter
            }
            catch
            {
                // Hash oluşturulamadıysa basit string birleştirme
                return string.Join("_", parameters.Select(p => p?.ToString() ?? "NULL"));
            }
        }
    }
}
