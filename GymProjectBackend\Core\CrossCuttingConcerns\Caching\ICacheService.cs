using System;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache servisi için ana interface
    /// Basit ve etkili cache yönetimi sağlar
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// C<PERSON>'den veri getirir
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="key">Cache anahtarı</param>
        /// <returns>Cache'deki veri veya default değer</returns>
        T Get<T>(string key);

        /// <summary>
        /// Cache'e veri kaydeder
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="key">Cache anahtarı</param>
        /// <param name="value">Kaydedilecek veri</param>
        /// <param name="durationMinutes">Cache süresi (dakika)</param>
        void Set<T>(string key, T value, int durationMinutes = 30);

        /// <summary>
        /// Belirli bir cache anahtarını siler
        /// </summary>
        /// <param name="key">Silinecek cache anahtarı</param>
        void Remove(string key);

        /// <summary>
        /// Pattern'e uyan tüm cache anahtarlarını siler
        /// </summary>
        /// <param name="pattern">Silinecek pattern (örn: T5:E_Member:*)</param>
        void RemoveByPattern(string pattern);

        /// <summary>
        /// Belirli bir entity'nin tüm cache'lerini temizler
        /// </summary>
        /// <param name="entityName">Entity adı (örn: Member, Product)</param>
        void ClearEntity(string entityName);

        /// <summary>
        /// Belirli bir tenant'ın tüm cache'lerini temizler
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        void ClearTenant(int tenantId);

        /// <summary>
        /// Cache'de belirli bir anahtar var mı kontrol eder
        /// </summary>
        /// <param name="key">Kontrol edilecek anahtar</param>
        /// <returns>Var ise true, yok ise false</returns>
        bool Exists(string key);

        /// <summary>
        /// Cache istatistiklerini getirir
        /// </summary>
        /// <returns>Cache istatistikleri</returns>
        CacheStatistics GetStatistics();
    }

    /// <summary>
    /// Cache istatistikleri
    /// </summary>
    public class CacheStatistics
    {
        public long HitCount { get; set; }
        public long MissCount { get; set; }
        public long SetCount { get; set; }
        public double HitRatio => HitCount + MissCount > 0 ? (double)HitCount / (HitCount + MissCount) * 100 : 0;
        public int TotalKeys { get; set; }
        public long MemoryUsageBytes { get; set; }

        public void RecordHit() => HitCount++;
        public void RecordMiss() => MissCount++;
        public void RecordSet() => SetCount++;
    }
}
