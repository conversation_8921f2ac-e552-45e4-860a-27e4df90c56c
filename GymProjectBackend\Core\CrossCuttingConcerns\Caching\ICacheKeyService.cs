namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache key oluşturma servisi
    /// Tenant bazlı cache key'leri yönet<PERSON>
    /// </summary>
    public interface ICacheKeyService
    {
        /// <summary>
        /// Genel cache key oluşturur
        /// </summary>
        /// <param name="entityName">Entity adı (örn: Member, Product)</param>
        /// <param name="operation">İşlem adı (örn: GetAll, GetById)</param>
        /// <param name="parameters">İşlem parametreleri</param>
        /// <returns>Oluşturulan cache key</returns>
        string GenerateKey(string entityName, string operation, params object[] parameters);

        /// <summary>
        /// Liste işlemleri için cache key oluşturur
        /// </summary>
        /// <param name="entityName">Entity adı</param>
        /// <param name="operation"><PERSON><PERSON><PERSON> adı (varsayılan: GetAll)</param>
        /// <returns>Liste cache key'i</returns>
        string GenerateListKey(string entityName, string operation = "GetAll");

        /// <summary>
        /// Detay işlemleri için cache key oluşturur
        /// </summary>
        /// <param name="entityName">Entity adı</param>
        /// <param name="id">Entity ID'si</param>
        /// <returns>Detay cache key'i</returns>
        string GenerateDetailKey(string entityName, object id);

        /// <summary>
        /// Pattern key oluşturur (temizleme işlemleri için)
        /// </summary>
        /// <param name="entityName">Entity adı</param>
        /// <returns>Pattern key</returns>
        string GeneratePatternKey(string entityName);

        /// <summary>
        /// Tenant pattern key oluşturur
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Tenant pattern key</returns>
        string GenerateTenantPatternKey(int tenantId);
    }
}
