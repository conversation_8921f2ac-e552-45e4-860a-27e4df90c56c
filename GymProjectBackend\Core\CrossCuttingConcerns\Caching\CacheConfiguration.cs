using System.Collections.Generic;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache konfigürasyon ayarları
    /// Entity bazlı cache sürelerini ve ayarlarını yönetir
    /// </summary>
    public class CacheConfiguration
    {
        public Dictionary<string, EntityCacheSettings> EntitySettings { get; set; }

        public CacheConfiguration()
        {
            InitializeDefaultEntitySettings();
        }

        /// <summary>
        /// Belirli bir entity için cache ayarlarını getirir
        /// </summary>
        public EntityCacheSettings GetSettings(string entityName)
        {
            return EntitySettings.TryGetValue(entityName, out var settings) 
                ? settings 
                : new EntityCacheSettings(); // Default settings
        }

        /// <summary>
        /// Varsayılan entity ayarlarını başlatır
        /// </summary>
        private void InitializeDefaultEntitySettings()
        {
            EntitySettings = new Dictionary<string, EntityCacheSettings>
            {
                ["Member"] = new EntityCacheSettings
                {
                    ListCacheDuration = 5,      // Üye listesi 5 dakika
                    DetailCacheDuration = 10,   // Üye detayı 10 dakika
                    IsEnabled = true,
                    RelatedEntities = new[] { "Membership", "Payment", "MemberWorkoutProgram" }
                },
                ["WorkoutProgramTemplate"] = new EntityCacheSettings
                {
                    ListCacheDuration = 10,     // Program listesi 10 dakika
                    DetailCacheDuration = 30,   // Program detayı 30 dakika
                    IsEnabled = true,
                    RelatedEntities = new[] { "MemberWorkoutProgram" }
                },
                ["MemberWorkoutProgram"] = new EntityCacheSettings
                {
                    ListCacheDuration = 5,      // Atama listesi 5 dakika
                    DetailCacheDuration = 15,   // Atama detayı 15 dakika
                    IsEnabled = true,
                    RelatedEntities = new[] { "Member", "WorkoutProgramTemplate" }
                },
                ["Product"] = new EntityCacheSettings
                {
                    ListCacheDuration = 30,     // Ürün listesi 30 dakika
                    DetailCacheDuration = 60,   // Ürün detayı 60 dakika
                    IsEnabled = true,
                    RelatedEntities = new[] { "Transaction" }
                },
                ["Payment"] = new EntityCacheSettings
                {
                    ListCacheDuration = 10,     // Ödeme listesi 10 dakika
                    DetailCacheDuration = 30,   // Ödeme detayı 30 dakika
                    IsEnabled = true,
                    RelatedEntities = new[] { "Member" }
                },
                ["Membership"] = new EntityCacheSettings
                {
                    ListCacheDuration = 15,     // Üyelik listesi 15 dakika
                    DetailCacheDuration = 30,   // Üyelik detayı 30 dakika
                    IsEnabled = true,
                    RelatedEntities = new[] { "Member" }
                },
                ["MembershipType"] = new EntityCacheSettings
                {
                    ListCacheDuration = 60,     // Üyelik türü listesi 60 dakika
                    DetailCacheDuration = 120,  // Üyelik türü detayı 120 dakika
                    IsEnabled = true,
                    RelatedEntities = new[] { "Membership" }
                },
                ["City"] = new EntityCacheSettings
                {
                    ListCacheDuration = 1440,   // Şehir listesi 24 saat
                    DetailCacheDuration = 1440, // Şehir detayı 24 saat
                    IsEnabled = true,
                    RelatedEntities = new string[0] // İlgili entity yok
                },
                ["Town"] = new EntityCacheSettings
                {
                    ListCacheDuration = 1440,   // İlçe listesi 24 saat
                    DetailCacheDuration = 1440, // İlçe detayı 24 saat
                    IsEnabled = true,
                    RelatedEntities = new string[0] // İlgili entity yok
                },
                ["Exercise"] = new EntityCacheSettings
                {
                    ListCacheDuration = 120,    // Egzersiz listesi 2 saat
                    DetailCacheDuration = 240,  // Egzersiz detayı 4 saat
                    IsEnabled = true,
                    RelatedEntities = new[] { "WorkoutProgramTemplate" }
                }
            };
        }
    }

    /// <summary>
    /// Entity cache ayarları
    /// </summary>
    public class EntityCacheSettings
    {
        /// <summary>
        /// Liste cache süresi (dakika)
        /// </summary>
        public int ListCacheDuration { get; set; } = 30;

        /// <summary>
        /// Detay cache süresi (dakika)
        /// </summary>
        public int DetailCacheDuration { get; set; } = 60;

        /// <summary>
        /// Cache aktif mi
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// İlgili entity'ler (cache temizleme için)
        /// </summary>
        public string[] RelatedEntities { get; set; } = new string[0];
    }
}
