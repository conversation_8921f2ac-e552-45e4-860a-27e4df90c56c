using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Memory cache implementasyonu
    /// Ba<PERSON><PERSON>, hızlı ve etkili cache yönet<PERSON>
    /// </summary>
    public class MemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ICacheKeyService _keyService;
        private readonly ILogger<MemoryCacheService> _logger;
        private readonly CacheStatistics _statistics;

        public MemoryCacheService(
            IMemoryCache memoryCache, 
            ICacheKeyService keyService, 
            ILogger<MemoryCacheService> logger)
        {
            _memoryCache = memoryCache;
            _keyService = keyService;
            _logger = logger;
            _statistics = new CacheStatistics();
        }

        public T Get<T>(string key)
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out T value))
                {
                    _statistics.RecordHit();
                    _logger.LogDebug("Cache HIT: {Key}", key);
                    return value;
                }

                _statistics.RecordMiss();
                _logger.LogDebug("Cache MISS: {Key}", key);
                return default(T);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache GET error for key: {Key}", key);
                _statistics.RecordMiss();
                return default(T);
            }
        }

        public void Set<T>(string key, T value, int durationMinutes = 30)
        {
            try
            {
                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(durationMinutes),
                    SlidingExpiration = TimeSpan.FromMinutes(Math.Min(durationMinutes / 2, 10)),
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(key, value, options);
                _statistics.RecordSet();
                _logger.LogDebug("Cache SET: {Key} for {Duration} minutes", key, durationMinutes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache SET error for key: {Key}", key);
            }
        }

        public void Remove(string key)
        {
            try
            {
                _memoryCache.Remove(key);
                _logger.LogDebug("Cache REMOVE: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache REMOVE error for key: {Key}", key);
            }
        }

        public void ClearEntity(string entityName)
        {
            try
            {
                var pattern = _keyService.GeneratePatternKey(entityName);
                RemoveByPattern(pattern);
                _logger.LogInformation("Cache cleared for entity: {EntityName}", entityName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache CLEAR error for entity: {EntityName}", entityName);
            }
        }

        public void ClearTenant(int tenantId)
        {
            try
            {
                var pattern = _keyService.GenerateTenantPatternKey(tenantId);
                RemoveByPattern(pattern);
                _logger.LogInformation("Cache cleared for tenant: {TenantId}", tenantId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache CLEAR error for tenant: {TenantId}", tenantId);
            }
        }

        public void RemoveByPattern(string pattern)
        {
            try
            {
                var keysToRemove = GetCacheKeys()
                    .Where(key => IsPatternMatch(key, pattern))
                    .ToList();

                foreach (var key in keysToRemove)
                {
                    _memoryCache.Remove(key);
                }

                _logger.LogDebug("Removed {Count} cache entries matching pattern: {Pattern}", keysToRemove.Count, pattern);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cache REMOVE BY PATTERN error for pattern: {Pattern}", pattern);
            }
        }

        public bool Exists(string key)
        {
            return _memoryCache.TryGetValue(key, out _);
        }

        public CacheStatistics GetStatistics()
        {
            _statistics.TotalKeys = GetCacheKeys().Count();
            return _statistics;
        }

        /// <summary>
        /// Memory cache'deki tüm key'leri getirir
        /// </summary>
        private IEnumerable<string> GetCacheKeys()
        {
            try
            {
                var field = typeof(MemoryCache).GetField("_coherentState", BindingFlags.NonPublic | BindingFlags.Instance);
                if (field?.GetValue(_memoryCache) is not object coherentState) return Enumerable.Empty<string>();

                var entriesCollection = coherentState.GetType().GetProperty("EntriesCollection", BindingFlags.NonPublic | BindingFlags.Instance);
                if (entriesCollection?.GetValue(coherentState) is not IDictionary entries) return Enumerable.Empty<string>();

                return entries.Keys.Cast<object>().Select(k => k.ToString()).ToList();
            }
            catch
            {
                return Enumerable.Empty<string>();
            }
        }

        /// <summary>
        /// Pattern matching kontrolü
        /// </summary>
        private static bool IsPatternMatch(string key, string pattern)
        {
            if (pattern.EndsWith("*"))
            {
                var prefix = pattern[..^1]; // Son * karakterini çıkar
                return key.StartsWith(prefix);
            }
            return key == pattern;
        }
    }
}
