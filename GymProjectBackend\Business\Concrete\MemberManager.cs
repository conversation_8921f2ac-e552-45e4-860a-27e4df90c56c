using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace Business.Concrete
{
    public class MemberManager : IMemberService
    {
        IMemberDal _memberDal;
        IMembershipDal _membershipDal;
        IEntryExitHistoryService _entryExitHistoryService;
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;
        private readonly IQrCodeEncryptionService _qrCodeEncryptionService;
        private readonly ICacheService _cacheService;
        private readonly ICacheKeyService _keyService;
        private readonly CacheConfiguration _cacheConfig;
        private readonly ILogger<MemberManager> _logger;

        public MemberManager(
            IMemberDal memberDal,
            IMembershipDal membershipDal,
            IEntryExitHistoryService entryExitHistoryService,
            Core.Utilities.Security.CompanyContext.ICompanyContext companyContext,
            IQrCodeEncryptionService qrCodeEncryptionService,
            ICacheService cacheService,
            ICacheKeyService keyService,
            CacheConfiguration cacheConfig,
            ILogger<MemberManager> logger)
        {
            _memberDal = memberDal;
            _membershipDal = membershipDal;
            _entryExitHistoryService = entryExitHistoryService;
            _companyContext = companyContext;
            _qrCodeEncryptionService = qrCodeEncryptionService;
            _cacheService = cacheService;
            _keyService = keyService;
            _cacheConfig = cacheConfig;
            _logger = logger;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Member", "BranchCount")]
        public IDataResult<Dictionary<string, int>> GetBranchCounts()
        {
            var companyId = _companyContext.GetCompanyId();
            return _memberDal.GetBranchCounts(companyId);
        }


        [SecuredOperation("owner,admin")]
        public IDataResult<List<MemberEntryDto>> GetMemberEntriesByName(string name)
        {
            return new SuccessDataResult<List<MemberEntryDto>>(_memberDal.GetMemberEntriesByName(name));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 5, "MemberEntry", "SearchPaginated")]
        public IDataResult<PaginatedResult<MemberEntryDto>> GetMemberEntriesBySearchPaginated(MemberEntryPagingParameters parameters)
        {
            var result = _memberDal.GetMemberEntriesBySearchPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MemberEntryDto>>(result);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<Member>> GetAllPaginated(MemberPagingParameters parameters)
        {
            const string entityName = "Member";

            // Cache ayarlarını al
            var settings = _cacheConfig.GetSettings(entityName);
            if (!settings.IsEnabled)
            {
                var result = _memberDal.GetAllPaginated(parameters);
                return new SuccessDataResult<PaginatedResult<Member>>(result);
            }

            // Cache key oluştur (parametreler dahil)
            var cacheKey = _keyService.GenerateKey(entityName, "GetAllPaginated",
                parameters.PageNumber, parameters.PageSize, parameters.SearchText ?? "", parameters.Gender ?? -1);

            // Cache'den dene
            var cachedData = _cacheService.Get<PaginatedResult<Member>>(cacheKey);
            if (cachedData != null)
            {
                _logger.LogDebug("Paginated members loaded from cache");
                return new SuccessDataResult<PaginatedResult<Member>>(cachedData, "Veriler cache'den getirildi");
            }

            // DB'den çek
            var dbResult = _memberDal.GetAllPaginated(parameters);

            // Cache'e kaydet
            _cacheService.Set(cacheKey, dbResult, settings.ListCacheDuration);

            _logger.LogDebug("Paginated members loaded from database and cached");
            return new SuccessDataResult<PaginatedResult<Member>>(dbResult, "Veriler veritabanından getirildi");
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MemberFilter>> GetMemberDetailsPaginated(MemberPagingParameters parameters)
        {
            var result = _memberDal.GetMemberDetailsPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MemberFilter>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MemberFilter>> GetMembersByMultiplePackages(MemberPagingParameters parameters)
        {
            var result = _memberDal.GetMembersByMultiplePackages(parameters);
            return new SuccessDataResult<PaginatedResult<MemberFilter>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<Member>> GetMembersWithBalancePaginated(MemberPagingParameters parameters, string balanceFilter)
        {
            var result = _memberDal.GetMembersWithBalancePaginated(parameters, balanceFilter);
            return new SuccessDataResult<PaginatedResult<Member>>(result);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Member", "ActiveCount")]
        public IDataResult<int> GetTotalActiveMembers()
        {
            var companyId = _companyContext.GetCompanyId();
            return _memberDal.GetTotalActiveMembers(companyId);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Member", "RegisteredCount")]
        public IDataResult<int> GetTotalRegisteredMembers()
        {
            var companyId = _companyContext.GetCompanyId();
            return _memberDal.GetTotalRegisteredMembers(companyId);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 15, "Member", "GenderCount")]
        public IDataResult<Dictionary<string, int>> GetActiveMemberCounts()
        {
            var companyId = _companyContext.GetCompanyId();
            return _memberDal.GetActiveMemberCounts(companyId);
        }


        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberEntryDto>> GetTodayEntries(DateTime date)
        {
            return new SuccessDataResult<List<MemberEntryDto>>(_memberDal.GetTodayEntries(date));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 5, "MemberEntry", "TodayPaginated")]
        public IDataResult<PaginatedResult<MemberEntryDto>> GetTodayEntriesPaginated(MemberEntryPagingParameters parameters)
        {
            var result = _memberDal.GetTodayEntriesPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MemberEntryDto>>(result);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(MemberValidator))]
        [TransactionScopeAspect]
        public IResult Add(Member member)
        {
            // Önce cache temizle
            ClearMemberCaches();

            member.Name = member.Name.ToUpper();
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık ekleme işlemini DAL katmanına taşıdık
            var result = _memberDal.AddMemberWithUserManagement(member, companyId);

            // Başarılı ise tekrar temizle
            if (result.Success)
            {
                ClearMemberCaches();
                _logger.LogInformation("Member added and cache cleared: {MemberName}", member.Name);
            }

            return result;
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        public IResult Delete(int id)
        {
            // Önce cache temizle
            ClearMemberCaches();

            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık silme işlemini DAL katmanına taşıdık
            var result = _memberDal.DeleteMemberWithUserManagement(id, companyId);

            // Başarılı ise tekrar temizle
            if (result.Success)
            {
                ClearMemberCaches();
                _logger.LogInformation("Member deleted and cache cleared: {MemberID}", id);
            }

            return result;
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<Member>> GetAll()
        {
            const string entityName = "Member";

            // Cache ayarlarını al
            var settings = _cacheConfig.GetSettings(entityName);
            if (!settings.IsEnabled)
            {
                return new SuccessDataResult<List<Member>>(_memberDal.GetAll());
            }

            // Cache key oluştur
            var cacheKey = _keyService.GenerateListKey(entityName, "GetAll");

            // Cache'den dene
            var cachedData = _cacheService.Get<List<Member>>(cacheKey);
            if (cachedData != null)
            {
                _logger.LogDebug("Members loaded from cache");
                return new SuccessDataResult<List<Member>>(cachedData, "Veriler cache'den getirildi");
            }

            // DB'den çek
            var members = _memberDal.GetAll();

            // Cache'e kaydet
            _cacheService.Set(cacheKey, members, settings.ListCacheDuration);

            _logger.LogDebug("Members loaded from database and cached");
            return new SuccessDataResult<List<Member>>(members, "Veriler veritabanından getirildi");
        }
        [SecuredOperation("owner,admin")]
        public IDataResult<List<MembeFilterDto>> GetMemberDetails()
        {
            return new SuccessDataResult<List<MembeFilterDto>>(_memberDal.GetMemberDetails());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MemberDetailWithHistoryDto> GetMemberDetailById(int memberId)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık hesaplama işlemini DAL katmanına taşıdık
            return _memberDal.GetMemberDetailByIdWithCalculations(memberId, companyId);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "Member", "Birthdays")]
        public IDataResult<List<MemberBirthdayDto>> GetUpcomingBirthdays(int days)
        {
            return new SuccessDataResult<List<MemberBirthdayDto>>(_memberDal.GetUpcomingBirthdays(days));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<GetActiveMemberDto>> GetActiveMembers()
        {
            return new SuccessDataResult<List<GetActiveMemberDto>>(_memberDal.GetActiveMembers());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberEntryExitHistoryDto>> GetMemberEntryExitHistory()
        {
            return new SuccessDataResult<List<MemberEntryExitHistoryDto>>(_memberDal.GetMemberEntryExitHistory());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberRemainingDayDto>> GetMemberRemainingDay()
        {
            return new SuccessDataResult<List<MemberRemainingDayDto>>(_memberDal.GetMemberRemainingDay());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(MemberValidator))]
        public IResult Update(Member member)
        {
            // Önce cache temizle
            ClearMemberCaches();

            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık güncelleme işlemini DAL katmanına taşıdık
            var result = _memberDal.UpdateMemberWithUserManagement(member, companyId);

            // Başarılı ise tekrar temizle
            if (result.Success)
            {
                ClearMemberCaches();
                _logger.LogInformation("Member updated and cache cleared: {MemberID}", member.MemberID);
            }

            return result;
        }
        //SECUREDOPERATION ASPECT KOYMA PUBLIC PING STATION
        [PerformanceAspect(2)]
        public IDataResult<MemberDetailDto> GetMemberRemainingDaysForScanNumber(string scanNumber)
        {
            // Şifreli QR kodu çöz
            var decryptResult = _qrCodeEncryptionService.DecryptQrToken(scanNumber);

            // QR kod çözülemezse hata döndür
            if (!decryptResult.Success || decryptResult.Data == null)
            {
                return new ErrorDataResult<MemberDetailDto>(null, decryptResult.Message ?? "Geçersiz QR kod.");
            }

            var decryptedData = decryptResult.Data;

            // Zaman geçerliliğini kontrol et
            if (!decryptedData.IsValid)
            {
                return new ErrorDataResult<MemberDetailDto>(null, decryptedData.ErrorMessage ?? "QR kodun süresi dolmuş.");
            }

            // DAL'dan veri al
            return _memberDal.GetMemberRemainingDaysForScanNumber(decryptedData.ScanNumber, _companyContext.GetCompanyId());
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByPhoneNumber(string phoneNumber)
        {
            var result = _memberDal.GetMemberQRByPhoneNumber(phoneNumber, _companyContext.GetCompanyId());

            if (result.Success && result.Data != null)
            {
                // QR kod şifreleme işlemini Manager'da yap
                string encryptedQRCode = _qrCodeEncryptionService.CreateEncryptedQrToken(
                    result.Data.Name.GetHashCode(), // Geçici çözüm - MemberID yerine
                    result.Data.ScanNumber
                );

                result.Data.ScanNumber = encryptedQRCode;
            }

            return result;
        }

        [SecuredOperation("owner,admin")]
        public IDataResult<List<Member>> GetByMemberId(int memberid)
        {
            return new SuccessDataResult<List<Member>>(_memberDal.GetAll(c => c.MemberID == memberid));
        }

        // SOLID prensiplerine uygun refactoring sonrası artık kullanılmayan helper metotlar kaldırıldı
        // GenerateUniqueQRCode, CreateNewUserForMember ve GenerateRandomPart metotları
        // DAL katmanına taşındı


        //Şuanda kullanılmayan bir metot ilerde lazım olabilir diye duruyor.
        [SecuredOperation("owner,admin,member")]
        [PerformanceAspect(3)]
        public IDataResult<Member> GetMemberByUserId(int userId)
        {
            var member = _memberDal.Get(m => m.UserID == userId && m.IsActive == true && m.CompanyID == _companyContext.GetCompanyId());
            if (member == null)
            {
                return new ErrorDataResult<Member>("Üye bulunamadı veya erişim yetkiniz yok.");
            }
            return new SuccessDataResult<Member>(member);
        }

        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        public IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByUserIdWithoutCompanyFilter(int userId)
        {
            var result = _memberDal.GetMemberQRByUserIdWithoutCompanyFilter(userId);

            if (result.Success && result.Data != null)
            {
                // QR kod şifreleme işlemini Manager'da yap
                // MemberID'yi DAL'dan alamadığımız için alternatif bir yöntem kullanıyoruz
                // Bu durumda member.ScanNumber zaten mevcut, sadece şifreleme yapıyoruz
                string encryptedQRCode = _qrCodeEncryptionService.CreateEncryptedQrToken(
                    result.Data.Name.GetHashCode(), // Geçici çözüm
                    result.Data.ScanNumber
                );

                result.Data.ScanNumber = encryptedQRCode;
            }

            return result;
        }

        /// <summary>
        /// Kullanıcının profil bilgilerini getirir (sadece member rolü)
        /// </summary>
        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        public IDataResult<MemberProfileDto> GetMemberProfileByUserId(int userId)
        {
            return _memberDal.GetMemberProfileByUserId(userId, _companyContext.GetCompanyId());
        }

        /// <summary>
        /// Kullanıcının profil bilgilerini günceller (sadece member rolü)
        /// User tablosunda: FirstName, LastName (tek kayıt)
        /// Member tablosunda: Adress, BirthDate (sadece mevcut şirketteki kayıt güncellenir)
        /// Not: Member.Name alanı güncellenmez (salon yöneticisi kontrolünde)
        /// </summary>
        [SecuredOperation("member")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult UpdateMemberProfile(int userId, MemberProfileUpdateDto profileUpdateDto)
        {
            // Önce cache temizle
            ClearMemberCaches();

            var result = _memberDal.UpdateMemberProfile(userId, _companyContext.GetCompanyId(), profileUpdateDto);

            // Başarılı ise tekrar temizle
            if (result.Success)
            {
                ClearMemberCaches();
                _logger.LogInformation("Member profile updated and cache cleared: {UserId}", userId);
            }

            return result;
        }

        /// <summary>
        /// Member ile ilgili tüm cache'leri temizler
        /// </summary>
        private void ClearMemberCaches()
        {
            try
            {
                // Ana entity cache'ini temizle
                _cacheService.ClearEntity("Member");

                // İlgili entity'lerin cache'lerini de temizle
                var settings = _cacheConfig.GetSettings("Member");
                foreach (var relatedEntity in settings.RelatedEntities)
                {
                    _cacheService.ClearEntity(relatedEntity);
                    _logger.LogDebug("Related entity cache cleared: {EntityName}", relatedEntity);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing member caches");
            }
        }
    }
}