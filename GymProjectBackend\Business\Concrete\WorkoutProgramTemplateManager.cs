using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class WorkoutProgramTemplateManager : IWorkoutProgramTemplateService
    {
        private readonly IWorkoutProgramTemplateDal _workoutProgramTemplateDal;
        private readonly ICompanyContext _companyContext;
        private readonly ICacheService _cacheService;
        private readonly ICacheKeyService _keyService;
        private readonly CacheConfiguration _cacheConfig;
        private readonly ILogger<WorkoutProgramTemplateManager> _logger;

        public WorkoutProgramTemplateManager(
            IWorkoutProgramTemplateDal workoutProgramTemplateDal,
            ICompanyContext companyContext,
            ICacheService cacheService,
            ICacheKeyService keyService,
            CacheConfiguration cacheConfig,
            ILogger<WorkoutProgramTemplateManager> logger)
        {
            _workoutProgramTemplateDal = workoutProgramTemplateDal;
            _companyContext = companyContext;
            _cacheService = cacheService;
            _keyService = keyService;
            _cacheConfig = cacheConfig;
            _logger = logger;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<WorkoutProgramTemplateListDto>> GetAll()
        {
            const string entityName = "WorkoutProgramTemplate";

            // Cache ayarlarını al
            var settings = _cacheConfig.GetSettings(entityName);
            if (!settings.IsEnabled)
            {
                var result = _workoutProgramTemplateDal.GetWorkoutProgramTemplateList();
                return new SuccessDataResult<List<WorkoutProgramTemplateListDto>>(result, Messages.WorkoutProgramsListed);
            }

            // Cache key oluştur
            var cacheKey = _keyService.GenerateListKey(entityName, "GetAll");

            // Cache'den dene
            var cachedData = _cacheService.Get<List<WorkoutProgramTemplateListDto>>(cacheKey);
            if (cachedData != null)
            {
                _logger.LogDebug("Workout program templates loaded from cache");
                return new SuccessDataResult<List<WorkoutProgramTemplateListDto>>(cachedData, "Veriler cache'den getirildi");
            }

            // DB'den çek
            var dbResult = _workoutProgramTemplateDal.GetWorkoutProgramTemplateList();

            // Cache'e kaydet
            _cacheService.Set(cacheKey, dbResult, settings.ListCacheDuration);

            _logger.LogDebug("Workout program templates loaded from database and cached");
            return new SuccessDataResult<List<WorkoutProgramTemplateListDto>>(dbResult, Messages.WorkoutProgramsListed);
        }

        [SecuredOperation("owner,admin")]
        [MultiTenantCacheAspect(duration: 30, "WorkoutProgramTemplate", "Detail")]
        [PerformanceAspect(3)]
        public IDataResult<WorkoutProgramTemplateDto> GetById(int templateId)
        {
            // SOLID prensiplerine uygun: Validation logic'i DAL katmanına taşıdık
            var templateResult = _workoutProgramTemplateDal.GetWorkoutProgramTemplateByIdWithValidation(templateId);
            if (!templateResult.Success)
            {
                return new ErrorDataResult<WorkoutProgramTemplateDto>(templateResult.Message);
            }

            var result = _workoutProgramTemplateDal.GetWorkoutProgramTemplateDetail(templateId);
            if (result == null)
            {
                return new ErrorDataResult<WorkoutProgramTemplateDto>(Messages.WorkoutProgramNotFound);
            }
            return new SuccessDataResult<WorkoutProgramTemplateDto>(result, Messages.WorkoutProgramDetailRetrieved);
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateAddValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        public IResult Add(WorkoutProgramTemplateAddDto templateAddDto)
        {
            // Önce cache temizle
            ClearWorkoutProgramCaches();

            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Business rules validation DAL katmanına taşındı
            var result = _workoutProgramTemplateDal.AddWorkoutProgramWithBusinessRules(templateAddDto, companyId);

            // Başarılı ise tekrar temizle
            if (result.Success)
            {
                ClearWorkoutProgramCaches();
                _logger.LogInformation("Workout program template added and cache cleared: {TemplateName}", templateAddDto.Name);
            }

            return result;
        }

        [SecuredOperation("owner,admin")]
        [ValidationAspect(typeof(WorkoutProgramTemplateUpdateValidator))]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        public IResult Update(WorkoutProgramTemplateUpdateDto templateUpdateDto)
        {
            // Önce cache temizle
            ClearWorkoutProgramCaches();

            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Business rules validation DAL katmanına taşındı
            var result = _workoutProgramTemplateDal.UpdateWorkoutProgramWithBusinessRules(templateUpdateDto, companyId);

            // Başarılı ise tekrar temizle
            if (result.Success)
            {
                ClearWorkoutProgramCaches();
                _logger.LogInformation("Workout program template updated and cache cleared: {TemplateId}", templateUpdateDto.WorkoutProgramTemplateID);
            }

            return result;
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int templateId)
        {
            // Önce cache temizle
            ClearWorkoutProgramCaches();

            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Validation logic DAL katmanına taşındı
            var result = _workoutProgramTemplateDal.SoftDeleteWorkoutProgramWithValidation(templateId, companyId);

            // Başarılı ise tekrar temizle
            if (result.Success)
            {
                ClearWorkoutProgramCaches();
                _logger.LogInformation("Workout program template deleted and cache cleared: {TemplateId}", templateId);
            }

            return result;
        }

        /// <summary>
        /// WorkoutProgramTemplate ile ilgili tüm cache'leri temizler
        /// </summary>
        private void ClearWorkoutProgramCaches()
        {
            try
            {
                // Ana entity cache'ini temizle
                _cacheService.ClearEntity("WorkoutProgramTemplate");

                // İlgili entity'lerin cache'lerini de temizle
                var settings = _cacheConfig.GetSettings("WorkoutProgramTemplate");
                foreach (var relatedEntity in settings.RelatedEntities)
                {
                    _cacheService.ClearEntity(relatedEntity);
                    _logger.LogDebug("Related entity cache cleared: {EntityName}", relatedEntity);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing workout program template caches");
            }
        }
    }
}
