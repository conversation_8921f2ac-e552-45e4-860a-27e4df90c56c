Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000210059F6C, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (0002102860C8, 0007FFFFBA68, 000000000000, 0002102684E0) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068EFF (0007FFFFBBC0, 000000000000, 000000000000, 00010044CE00) msys-2.0.dll+0x28EFF
0007FFFFBE90  00021006A225 (000000004000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
0007FFFFBE90  00021006A4A9 (000000000000, 0007FFFFBF38, 0007FFFFC114, 0000FFFFFFFF) msys-2.0.dll+0x2A4A9
0007FFFFBE90  000210193F2B (000000000000, 0007FFFFBF38, 0007FFFFC114, 0000FFFFFFFF) msys-2.0.dll+0x153F2B
0007FFFFBE90  00010042DB65 (000A00000004, 0007FFFFC124, 00010040DD62, 000000000010) bash.exe+0x2DB65
0000FFFFFFFF  00010043C4F8 (0000000000C2, 000A00000000, 000A00189B20, 000A00089DE0) bash.exe+0x3C4F8
000000000070  00010043E6BE (000A0004C680, 000200000001, 000210178DD2, 0007FFFFC120) bash.exe+0x3E6BE
000000000070  000100441B06 (000700000001, 000A00000000, 0007FFFFC210, 000000000000) bash.exe+0x41B06
000000000070  000100441D36 (000200000000, 000A00000000, 000000000000, 000000000000) bash.exe+0x41D36
000A00173E10  000100444B1F (0000000000A9, 000A0014D3F0, 000000000030, 0000000000A9) bash.exe+0x44B1F
000A00173E10  0001004151FF (000A0014D3F0, 000100626250, 8080808080808080, 0001004F6EF7) bash.exe+0x151FF
000A000EB5A0  00010041561E (000000000001, 000210268720, 000210178DD2, 000210268720) bash.exe+0x1561E
0000FFFFFFFF  00010041792C (000A00021590, 000000000000, 000A00021590, 000A001898F0) bash.exe+0x1792C
0000000000A9  00010041AC6A (000A000496D0, 000210268720, 000100620700, 000000000001) bash.exe+0x1AC6A
0000000000A9  0001004180FB (000000000000, 000000000001, 0002100AC638, 000A000E4080) bash.exe+0x180FB
000000000001  00010046F3A5 (000A0004A390, 000A00000414, 0000CE000000, 0000000000BE) bash.exe+0x6F3A5
000A00029930  00010046E127 (000A00000009, 000000000000, 000000000000, 0007FFFFCBEF) bash.exe+0x6E127
000000000000  00010046E2D5 (0002102ADAC0, 0002102686E0, 000000000000, 000000000005) bash.exe+0x6E2D5
000000000000  0001004EA48C (000A00002590, 000A00000160, 0002100C96E9, 000000000000) bash.exe+0xEA48C
0007FFFFCD30  000210047F01 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x7F01
000000000000  000210045AC3 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5AC3
0007FFFFFFF0  000210045B74 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x5B74
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB822E0000 ntdll.dll
7FFB81600000 KERNEL32.DLL
7FFB7FBD0000 KERNELBASE.dll
7FFB812D0000 USER32.dll
7FFB7FA70000 win32u.dll
000210040000 msys-2.0.dll
7FFB815D0000 GDI32.dll
7FFB7F930000 gdi32full.dll
7FFB7F730000 msvcp_win.dll
7FFB7F7E0000 ucrtbase.dll
7FFB80080000 advapi32.dll
7FFB81860000 msvcrt.dll
7FFB80A10000 sechost.dll
7FFB82180000 RPCRT4.dll
7FFB7EB50000 CRYPTBASE.DLL
7FFB7FAA0000 bcryptPrimitives.dll
7FFB819F0000 IMM32.DLL
